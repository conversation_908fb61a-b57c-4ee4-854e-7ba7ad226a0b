import translationStrings from "../../language/translations";
import BlogViewClient from "./BlogViewClient";
const logger = require("../../../../utils/logger");

async function fetchBlogPost(slug, lang) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3002";
    const startTime = Date.now();
    const response = await fetch(
      `${baseUrl}/api/blog/view/${slug}?lang=${lang}`,
      {
        method: "GET",
        headers: {
          "Accept-Language": lang,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );
    const endTime = Date.now();
    logger.info(`fetchBlogPost took ${endTime - startTime}ms`);

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error("Blog post not found");
      }
      throw new Error("Failed to fetch blog post");
    }
    return response.json();
  } catch (error) {
    logger.error("Error fetching blog post:", error);
    throw error;
  }
}

export default async function BlogPage({ params }) {
  const { slug, lang } = await params;
  const language = lang || "en";
  const startTime = Date.now();

  // Get language strings
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    home: rawStrings.home || "Home",
    blogTitle: rawStrings.blogTitle || "Blog",
    noBlogsFound: rawStrings.noBlogsFound || "Blog post not found",
  };

  try {
    // Fetch blog post data
    const blogPost = await fetchBlogPost(slug, language);
    const { title, meta_title, meta_description } = blogPost;

    // Use the fetched meta data, or fall back to blog title if meta data is missing
    const metaTitle = meta_title || title || "Default Title";
    const metaDescription = meta_description || title || "Default Description";

    const renderClientComponentStart = Date.now();
    const component = (
      <BlogViewClient
        blogPost={blogPost}
        language={language}
        langStrings={langStrings}
        metaTitle={metaTitle}
        metaDescription={metaDescription}
        isLoading={false}
      />
    );
    const renderClientComponentEnd = Date.now();
    logger.info(
      `Rendering BlogViewClient in BlogPage took ${
        renderClientComponentEnd - renderClientComponentStart
      }ms`
    );

    return component;
  } catch (error) {
    logger.error("Error in BlogPage:", error);
    return (
      <BlogViewClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        isLoading={false}
      />
    );
  } finally {
    const endTime = Date.now();
    logger.info(`BlogPage took ${endTime - startTime}ms`);
  }
}
