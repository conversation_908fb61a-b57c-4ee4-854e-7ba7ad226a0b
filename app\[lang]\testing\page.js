(function () {
  const IFRAME_HEIGHT = "70vh";
  const PRIMARY_COLOR = "#05aca5";
  const SECONDARY_COLOR = "#29469a";
  const BORDER_RADIUS = "8px";
  const STORAGE_KEY = "iframe-expanded-state";

  function isMobileScreen() {
    return window.matchMedia("(max-width: 767px)").matches;
  }

  const createIframe = () => {
    const wrapper = document.createElement("div");
    wrapper.style.width = "100%";
    wrapper.style.position = "relative";
    wrapper.style.borderRadius = BORDER_RADIUS;
    wrapper.style.overflow = "hidden";
    wrapper.style.transition = "height 0.3s ease-in-out";

    let isMobile = isMobileScreen();
    let hasBeenClicked = true;

    wrapper.style.height = IFRAME_HEIGHT;

    const iframe = document.createElement("iframe");
    iframe.src = "http://localhost:3000/chat";
    iframe.style.width = "100%";
    iframe.style.height = "100%";
    iframe.style.border = "none";

    const pulseElement = document.createElement("div");
    pulseElement.style.position = "absolute";
    pulseElement.style.top = "0";
    pulseElement.style.left = "0";
    pulseElement.style.right = "0";
    pulseElement.style.bottom = "0";
    pulseElement.style.pointerEvents = "none";
    pulseElement.style.borderRadius = BORDER_RADIUS;
    pulseElement.style.zIndex = "2";

    if (!document.getElementById("iframe-pulse-animation")) {
      const style = document.createElement("style");
      style.id = "iframe-pulse-animation";
      style.textContent = `
                .iframe-pulse {
                    box-shadow: 0 0 16px 4px ${PRIMARY_COLOR}55, 0 0 32px 8px ${SECONDARY_COLOR}33;
                }

                .iframe-clicked, .iframe-hovered {
                    background: none !important;
                    animation: none !important;
                    box-shadow: 0 0 10px ${PRIMARY_COLOR}22;
                }
            `;
      document.head.appendChild(style);
    }

    pulseElement.classList.add("iframe-pulse");

    wrapper.appendChild(iframe);
    wrapper.appendChild(pulseElement);

    const handleClick = () => {
      hasBeenClicked = true;
      wrapper.style.height = IFRAME_HEIGHT;
      pulseElement.classList.remove("iframe-pulse");
      pulseElement.classList.add("iframe-clicked");
      try {
        sessionStorage.setItem(STORAGE_KEY, "true");
      } catch (e) {
        console.warn("Could not save expanded state:", e);
      }
    };

    wrapper.addEventListener("click", handleClick);
    iframe.addEventListener("click", handleClick);

    wrapper.addEventListener("mouseenter", () => {
      if (!hasBeenClicked) {
        pulseElement.classList.remove("iframe-pulse");
        pulseElement.classList.add("iframe-hovered");
      }
    });

    wrapper.addEventListener("mouseleave", () => {
      if (!hasBeenClicked) {
        pulseElement.classList.remove("iframe-hovered");
        pulseElement.classList.add("iframe-pulse");
      }
    });

    hasBeenClicked = true;
    wrapper.style.height = IFRAME_HEIGHT;
    pulseElement.classList.remove("iframe-pulse");
    pulseElement.classList.add("iframe-clicked");

    window.addEventListener("message", function (event) {
      if (event.origin !== "http://localhost:3000") {
        return;
      }

      const { type, key, value } = event.data;

      switch (type) {
        case "get_cookie":
          const cookieValue = getCookie(key);
          iframe.contentWindow.postMessage(
            {
              type: "cookie_response",
              key,
              value: cookieValue,
            },
            "*"
          );
          break;

        case "set_cookie":
          setCookie(key, value);
          break;

        case "delete_cookie":
          deleteCookie(key);
          break;

        case "set_local_storage":
          setLocalStorageItem(key, value);
          break;

        case "get_local_storage":
          const localStorageValue = getLocalStorageItem(key);
          iframe.contentWindow.postMessage(
            {
              type: "local_storage_response",
              key,
              value: localStorageValue,
            },
            "*"
          );
          break;

        case "iframe_clicked":
          handleClick();
          break;

        case "get_page_data":
          const pageData = {
            url: window.location.href,
            title: document.querySelector("h1")?.textContent || document.title,
          };
          iframe.contentWindow.postMessage(
            {
              type: "page_data_response",
              pageData,
            },
            "*"
          );
          break;

        case "redirect_to_wa":
          (async () => {
            try {
              const response = await fetch("http://ip-api.com/json/");
              const data = await response.json();
              if (data.status === "success") {
                console.log("[IFRAME]User country:", data.country);
                const redirectUrl =
                  data.country === "United States"
                    ? "https://app.meetaugust.ai"
                    : "https://meetaugust.ai/wa";
                window.open(redirectUrl, "_blank");
              } else {
                // Fallback to default URL if country check fails
                window.open("https://meetaugust.ai/wa", "_blank");
              }
            } catch (error) {
              console.error("[IFRAME]Error fetching country:", error);
              // Fallback to default URL if there's an error
              window.open("https://meetaugust.ai/wa", "_blank");
            }
          })();
          break;
      }
    });

    window.addEventListener("click", (event) => {
      if (isMobile) return;
      if (!wrapper.contains(event.target)) {
        if (hasBeenClicked) {
          wrapper.style.height = IFRAME_HEIGHT;
          hasBeenClicked = false;
          pulseElement.classList.remove("iframe-clicked");
          pulseElement.classList.add("iframe-pulse");
          try {
            sessionStorage.setItem(STORAGE_KEY, "false");
          } catch (e) {
            console.warn("Could not save expanded state:", e);
          }
        }
      }
    });

    return wrapper;
  };

  function getCookie(name) {
    try {
      const match = document.cookie.match(
        new RegExp("(^| )" + name + "=([^;]+)")
      );
      const value = match ? match[2] : null;
      return value;
    } catch (error) {
      return null;
    }
  }

  function setCookie(name, value) {
    const maxAge = 60 * 60 * 24 * 30;
    document.cookie = `${name}=${value};path=/;max-age=${maxAge}`;
  }

  function deleteCookie(name) {
    document.cookie = `${name}=;path=/;expires=Thu, 01 Jan 1970 00:00:01 GMT`;
  }

  function setLocalStorageItem(name, value) {
    try {
      localStorage.setItem(name, value);
    } catch (error) {
      console.error("Error setting localStorage item:", error);
    }
  }

  function getLocalStorageItem(name) {
    try {
      const item = localStorage.getItem(name);
      return item !== null ? item : false;
    } catch (error) {
      console.error("Error getting localStorage item:", error);
      return false;
    }
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }

  function init() {
    const elements = document.getElementsByClassName("chat-embed");
    for (const element of elements) {
      element.appendChild(createIframe());
    }
  }
})();
