import DetailViewClient from "../../../../components/shared/DetailViewClient";
import { getConditionMetadata } from "../../../../api/diseases-conditions/meta/getMetaData";
import translationStrings from "../../language/translations";
const logger = require("../../../../utils/logger");

async function fetchCondition(slug, lang) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3002";
    const response = await fetch(
      `${baseUrl}/api/diseases-conditions/view/${slug}?lang=${lang}`,
      {
        method: "GET",
        headers: {
          "Accept-Language": lang,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch condition");
    }
    return response.json();
  } catch (error) {
    logger.error("Error fetching condition:", error);
    throw error;
  }
}

function extractFirstH1FromHtml(html) {
  if (!html) return null;
  const match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
  return match ? match[1].trim() : null;
}

function extractFirstPFromHtml(html) {
  if (!html) return null;
  const match = html.match(/<p[^>]*>(.*?)<\/p>/i);
  return match ? match[1].trim() : null;
}

export async function generateMetadata({ params }) {
  const { slug, lang } = params;
  const language = lang || "en";
  const condition = await fetchCondition(slug, language);
  const firstHeading = extractFirstH1FromHtml(condition.sections);
  const firstPTag = extractFirstPFromHtml(condition.sections);
  // console.log(firstPTag,'lkanlasnkldkasdans');
  return {
    title: firstHeading || "Default title",
    description:
      condition.meta_description ||
      firstPTag.split(".")[0] + "." ||
      "Default Description",
    openGraph: {
      title: firstHeading || "Default title",
      description:
        condition.meta_description ||
        firstPTag.split(".")[0] + "." ||
        "Default Description",
    },
  };
}

export default async function ConditionPage({ params }) {
  const { slug, lang } = params;
  const language = lang || "en";

  // Create a safe version of langStrings without functions
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    // Remove the function property
    noConditionsFound: undefined,
  };

  try {
    // Fetch condition data server-side
    const condition = await fetchCondition(slug, language);
    const firstHeading = extractFirstH1FromHtml(condition.sections);

    const breadcrumbItems = [
      { text: langStrings.home, href: `/${language}` },
      { text: langStrings.title, href: `/${language}/diseases-conditions` },
      { text: condition?.name || "" },
    ];

    return (
      <DetailViewClient
        condition={condition}
        breadcrumbItems={breadcrumbItems}
        metaTitle={firstHeading}
        metaDescription={condition.meta_description}
        firstHeading={firstHeading}
        language={language}
        langStrings={langStrings}
      />
    );
  } catch (error) {
    logger.error("Error in ConditionPage:", error);
    return (
      <DetailViewClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        breadcrumbItems={[
          { text: langStrings.home, href: `/${language}` },
          { text: langStrings.title, href: `/${language}/diseases-conditions` },
        ]}
        metaTitle={""}
        metaDescription={""}
        firstHeading={""}
      />
    );
  }
}
